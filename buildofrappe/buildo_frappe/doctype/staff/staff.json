{"actions": [], "allow_rename": 1, "autoname": "format:{staff_name}", "creation": "2025-07-28 22:24:06.045568", "doctype": "DocType", "engine": "InnoDB", "field_order": ["staff_name", "aadhar_no", "pan_no", "joining_date", "leaving_date", "column_break_hqez", "salary", "contact_no", "emergency_contact_person", "emergency_contact_no", "is_active"], "fields": [{"fieldname": "staff_name", "fieldtype": "Data", "label": "Staff Name"}, {"fieldname": "aadhar_no", "fieldtype": "Data", "label": "<PERSON>adhar No"}, {"fieldname": "pan_no", "fieldtype": "Data", "label": "Pan No"}, {"fieldname": "joining_date", "fieldtype": "Date", "label": "Joining Date"}, {"fieldname": "leaving_date", "fieldtype": "Date", "label": "Leaving Date"}, {"fieldname": "column_break_hqez", "fieldtype": "Column Break"}, {"fieldname": "salary", "fieldtype": "Float", "label": "Salary"}, {"default": "+91-", "fieldname": "contact_no", "fieldtype": "Phone", "label": "Contact No"}, {"fieldname": "emergency_contact_person", "fieldtype": "Data", "label": "Emergency Contact Person"}, {"default": "+91-", "fieldname": "emergency_contact_no", "fieldtype": "Phone", "label": "Emergency Contact No"}, {"default": "0", "fieldname": "is_active", "fieldtype": "Check", "label": "Is Active"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-09-20 14:16:24.564320", "modified_by": "Administrator", "module": "Buildo Frappe", "name": "Staff", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}