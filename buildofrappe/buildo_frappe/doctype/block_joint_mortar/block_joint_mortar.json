{"actions": [], "allow_rename": 1, "autoname": "format:{project}-{supplier}-{#######}", "creation": "2025-06-19 15:03:03.155900", "doctype": "DocType", "engine": "InnoDB", "field_order": ["date", "project", "supplier", "remarks", "column_break_bkfj", "brand", "challan_no", "vehicle_no", "qty_bags"], "fields": [{"fieldname": "date", "fieldtype": "Date", "label": "Date"}, {"fieldname": "project", "fieldtype": "Link", "label": "Project", "options": "Projects"}, {"fieldname": "supplier", "fieldtype": "Link", "label": "Supplier", "options": "Suppliers"}, {"fieldname": "remarks", "fieldtype": "Small Text", "label": "Remarks"}, {"fieldname": "column_break_bkfj", "fieldtype": "Column Break"}, {"fieldname": "brand", "fieldtype": "Data", "label": "Brand"}, {"fieldname": "challan_no", "fieldtype": "Data", "label": "Challan No"}, {"fieldname": "vehicle_no", "fieldtype": "Float", "label": "Vehicle No"}, {"fieldname": "qty_bags", "fieldtype": "Float", "label": "<PERSON><PERSON> Bags"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-09-20 14:37:52.071855", "modified_by": "Administrator", "module": "Buildo Frappe", "name": "Block Joint Mortar", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}