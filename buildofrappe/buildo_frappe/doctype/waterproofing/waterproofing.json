{"actions": [], "allow_rename": 1, "autoname": "format:MR-{Waterproofing}-{#######}", "creation": "2025-09-22 17:06:51.330547", "doctype": "DocType", "engine": "InnoDB", "field_order": ["date", "project", "supplier", "remarks", "column_break_pdub", "material", "challan_no", "vehicle_no", "qty"], "fields": [{"fieldname": "date", "fieldtype": "Date", "label": "Date"}, {"fieldname": "project", "fieldtype": "Link", "label": "Project", "options": "Projects"}, {"fieldname": "supplier", "fieldtype": "Link", "label": "Supplier", "options": "Suppliers"}, {"fieldname": "remarks", "fieldtype": "Small Text", "label": "Remarks"}, {"fieldname": "column_break_pdub", "fieldtype": "Column Break"}, {"fieldname": "challan_no", "fieldtype": "Data", "label": "Challan No"}, {"fieldname": "vehicle_no", "fieldtype": "Data", "label": "Vehicle No"}, {"fieldname": "qty", "fieldtype": "Data", "label": "Qty"}, {"fieldname": "material", "fieldtype": "Data", "label": "Material"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-09-22 17:36:11.829345", "modified_by": "Administrator", "module": "Buildo Frappe", "name": "Waterproofing", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}