{"actions": [], "allow_rename": 1, "autoname": "format:{project}-{grade}-{member_id}-{#######}", "creation": "2025-06-19 15:20:49.809289", "doctype": "DocType", "engine": "InnoDB", "field_order": ["date_of_casting", "column_break_zewr", "project", "column_break_fgfc", "grade", "column_break_miky", "member_id", "section_break_pzuq", "table_dkei", "section_break_tzru", "remarks"], "fields": [{"fieldname": "date_of_casting", "fieldtype": "Date", "label": "Date of casting"}, {"fieldname": "column_break_zewr", "fieldtype": "Column Break"}, {"fieldname": "project", "fieldtype": "Link", "label": "Project", "options": "Projects"}, {"fieldname": "column_break_fgfc", "fieldtype": "Column Break"}, {"fieldname": "grade", "fieldtype": "Select", "label": "Grade", "options": "M10\nM15\nM20\nM25\nM30\nM35\nM40\nM45\nM50\nM55\nM60\nM65\nM70\nM75\nM80\nM85\nM90\nM95\nM100"}, {"fieldname": "member_id", "fieldtype": "Data", "label": "Member ID"}, {"fieldname": "section_break_pzuq", "fieldtype": "Section Break"}, {"fieldname": "table_dkei", "fieldtype": "Table", "options": "Cube Table"}, {"fieldname": "section_break_tzru", "fieldtype": "Section Break"}, {"fieldname": "remarks", "fieldtype": "Small Text", "label": "Remarks"}, {"fieldname": "column_break_miky", "fieldtype": "Column Break"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-09-20 14:43:33.254155", "modified_by": "Administrator", "module": "Buildo Frappe", "name": "C<PERSON>", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}