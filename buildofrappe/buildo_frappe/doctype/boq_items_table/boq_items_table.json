{"actions": [], "allow_rename": 1, "creation": "2025-06-18 18:31:27.625848", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["description", "uom", "qty", "rate", "amount", "status"], "fields": [{"fieldname": "description", "fieldtype": "Small Text", "in_list_view": 1, "label": "Description"}, {"fieldname": "uom", "fieldtype": "Data", "in_list_view": 1, "label": "UOM"}, {"fieldname": "qty", "fieldtype": "Float", "in_list_view": 1, "label": "Qty"}, {"fieldname": "rate", "fieldtype": "Float", "in_list_view": 1, "label": "Rate"}, {"fieldname": "amount", "fieldtype": "Float", "in_list_view": 1, "label": "Amount"}, {"default": "Not Started", "fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "label": "Status", "options": "\nNot Started\nCompleted"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2025-09-24 15:56:10.035032", "modified_by": "Administrator", "module": "Buildo Frappe", "name": "BOQ Items Table", "owner": "Administrator", "permissions": [], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}