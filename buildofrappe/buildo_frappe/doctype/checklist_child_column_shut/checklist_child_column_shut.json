{"actions": [], "allow_rename": 1, "creation": "2025-09-16 18:08:52.188114", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["particulars", "yes", "no", "remarks"], "fields": [{"fieldname": "particulars", "fieldtype": "Data", "in_list_view": 1, "label": "Particulars"}, {"default": "0", "fieldname": "yes", "fieldtype": "Check", "in_list_view": 1, "label": "Yes"}, {"default": "0", "fieldname": "no", "fieldtype": "Check", "in_list_view": 1, "label": "No"}, {"fieldname": "remarks", "fieldtype": "Data", "in_list_view": 1, "label": "Remarks"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2025-09-22 11:37:45.395729", "modified_by": "Administrator", "module": "Buildo Frappe", "name": "Checklist_child_column_shut", "owner": "Administrator", "permissions": [], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}