{"actions": [], "allow_rename": 1, "autoname": "format:CSCHDL-{#####}", "creation": "2025-07-28 22:47:01.169232", "doctype": "DocType", "engine": "InnoDB", "field_order": ["schedule_id", "schedule_for", "column_break_jauj", "project", "contractor", "retention", "section_break_abmw", "rate", "column_break_vvme", "area", "column_break_psqw", "total_amount", "column_break_yoiw", "t_parts", "column_break_cbwl", "per_part_amount", "section_break_hsbb", "table_ydsq", "section_break_xliw", "parts_total", "column_break_jjlh", "amount_total", "column_break_xxnq", "retention_amount_total"], "fields": [{"fieldname": "schedule_id", "fieldtype": "Data", "label": "Schedule Id"}, {"fieldname": "schedule_for", "fieldtype": "Data", "label": "Schedule For", "placeholder": "eg Brick Work, RCC.."}, {"fieldname": "column_break_jauj", "fieldtype": "Column Break"}, {"fieldname": "project", "fieldtype": "Link", "label": "Project", "options": "Projects"}, {"fieldname": "contractor", "fieldtype": "Link", "label": "Contractor", "options": "Contractors"}, {"fieldname": "retention", "fieldtype": "Float", "label": "Retention %"}, {"fieldname": "section_break_abmw", "fieldtype": "Section Break"}, {"fieldname": "rate", "fieldtype": "Float", "label": "Rate"}, {"fieldname": "column_break_vvme", "fieldtype": "Column Break"}, {"fieldname": "area", "fieldtype": "Float", "label": "Area"}, {"fieldname": "column_break_psqw", "fieldtype": "Column Break"}, {"fieldname": "total_amount", "fieldtype": "Float", "label": "Total Amount", "read_only": 1}, {"fieldname": "column_break_yoiw", "fieldtype": "Column Break"}, {"fieldname": "t_parts", "fieldtype": "Float", "label": "Total Parts"}, {"fieldname": "column_break_cbwl", "fieldtype": "Column Break"}, {"fieldname": "per_part_amount", "fieldtype": "Float", "label": "Per Part Amount", "read_only": 1}, {"fieldname": "section_break_hsbb", "fieldtype": "Section Break"}, {"fieldname": "table_ydsq", "fieldtype": "Table", "options": "C Schedule Table"}, {"fieldname": "section_break_xliw", "fieldtype": "Section Break"}, {"fieldname": "parts_total", "fieldtype": "Float", "label": "Parts Total", "read_only": 1}, {"fieldname": "column_break_jjlh", "fieldtype": "Column Break"}, {"fieldname": "amount_total", "fieldtype": "Float", "label": "Amount Total", "read_only": 1}, {"fieldname": "column_break_xxnq", "fieldtype": "Column Break"}, {"fieldname": "retention_amount_total", "fieldtype": "Float", "label": "Retention Amount Total", "read_only": 1}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-09-20 16:41:36.013635", "modified_by": "Administrator", "module": "Buildo Frappe", "name": "C Schedule", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}