// Copyright (c) 2025, <PERSON><PERSON> and contributors
// For license information, please see license.txt

frappe.ui.form.on("C Schedule", {
    refresh(frm) {
        // Initial calculation on refresh if data exists
        calculate_main_amounts(frm);
        frm.doc.table_ydsq.forEach(row => {
            calculate_row_amounts(frm, row);
        });
        calculate_totals(frm);
    },

    rate(frm) {
        calculate_main_amounts(frm);
        frm.doc.table_ydsq.forEach(row => {
            calculate_row_amounts(frm, row);
        });
        calculate_totals(frm);
    },

    area(frm) {
        calculate_main_amounts(frm);
        frm.doc.table_ydsq.forEach(row => {
            calculate_row_amounts(frm, row);
        });
        calculate_totals(frm);
    },

    t_parts(frm) {
        calculate_main_amounts(frm);
        frm.doc.table_ydsq.forEach(row => {
            calculate_row_amounts(frm, row);
        });
        calculate_totals(frm);
    },

    retention(frm) {
        frm.doc.table_ydsq.forEach(row => {
            calculate_row_amounts(frm, row);
        });
        calculate_totals(frm);
    }
});

frappe.ui.form.on("C Schedule Table", {
    part(frm, cdt, cdn) {
        const row = frappe.get_doc(cdt, cdn);
        calculate_row_amounts(frm, row);
        calculate_totals(frm);
    },

    status(frm, cdt, cdn) {
        const row = frappe.get_doc(cdt, cdn);
        if (row.status === "Completed") {
            row.c_date = frappe.datetime.get_today();
        } else {
            row.c_date = null; // Clear date if status is not completed
        }
        frm.refresh_field("table_ydsq");
    }
});

// Calculate main amounts: Total Amount and Per Part Amount
function calculate_main_amounts(frm) {
    const rate = parseFloat(frm.doc.rate) || 0;
    const area = parseFloat(frm.doc.area) || 0;
    const t_parts = parseFloat(frm.doc.t_parts) || 0;

    // Total Amount = rate * area
    const total_amount = rate * area;
    frm.set_value("total_amount", total_amount);

    // Per Part Amount = total amount / Total Parts
    const per_part_amount = t_parts > 0 ? total_amount / t_parts : 0;
    frm.set_value("per_part_amount", per_part_amount);
}

// Calculate row amounts: amount and retention amount
function calculate_row_amounts(frm, row) {
    const per_part_amount = parseFloat(frm.doc.per_part_amount) || 0;
    const part = parseFloat(row.part) || 0;
    const retention_percent = parseFloat(frm.doc.retention) || 0;

    // amount = part * per part amount
    row.amount = part * per_part_amount;

    // retention amount = retention * amount / 100
    row.retention_amount = (retention_percent * row.amount) / 100;

    frm.refresh_field("table_ydsq");
}

// Calculate totals for the table
function calculate_totals(frm) {
    let parts_total = 0;
    let amount_total = 0;
    let retention_amount_total = 0;

    frm.doc.table_ydsq.forEach(row => {
        parts_total += parseFloat(row.part) || 0;
        amount_total += parseFloat(row.amount) || 0;
        retention_amount_total += parseFloat(row.retention_amount) || 0;
    });

    frm.set_value("parts_total", parts_total);
    frm.set_value("amount_total", amount_total);
    frm.set_value("retention_amount_total", retention_amount_total);

    // Validation: Check if parts total exceeds total parts
    const t_parts = parseFloat(frm.doc.t_parts) || 0;
    if (parts_total > t_parts) {
        frappe.msgprint(__("Parts Total ({0}) cannot be greater than Total Parts ({1})", [parts_total, t_parts]));
    }
}
