{"actions": [], "allow_rename": 1, "autoname": "field:exp_no", "creation": "2025-06-18 18:02:45.427654", "doctype": "DocType", "engine": "InnoDB", "field_order": ["exp_no", "remarks", "column_break_yqlu", "date", "project", "amount"], "fields": [{"fieldname": "exp_no", "fieldtype": "Data", "label": "Exp No", "unique": 1}, {"fieldname": "remarks", "fieldtype": "Small Text", "label": "Remarks"}, {"fieldname": "column_break_yqlu", "fieldtype": "Column Break"}, {"fieldname": "date", "fieldtype": "Date", "label": "Date"}, {"fieldname": "project", "fieldtype": "Link", "label": "Project", "options": "Projects"}, {"fieldname": "amount", "fieldtype": "Float", "label": "Amount"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-09-20 15:40:46.413178", "modified_by": "Administrator", "module": "Buildo Frappe", "name": "Misc Expense", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}