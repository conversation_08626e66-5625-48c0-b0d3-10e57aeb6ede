// Copyright (c) 2025, <PERSON><PERSON> and contributors
// For license information, please see license.txt

frappe.ui.form.on("Hajri", {
    refresh(frm) {
        frm.trigger("calculate_totals");
    },

    calculate_totals(frm) {
        let total_amount = 0;
        let total_advance = 0;
        let total_net_payable = 0;

        if (frm.doc.table_xwtx) {
            frm.doc.table_xwtx.forEach(function(row) {
                total_amount += flt(row.amount || 0);
                total_advance += flt(row.advances || 0);
                total_net_payable += flt(row.net_payable || 0);
            });
        }

        frm.set_value("total_amount", total_amount);
        frm.set_value("total_advance", total_advance);
        frm.set_value("total_net_payable", total_net_payable);
    }
});

frappe.ui.form.on("Hajri Table", {
    rate: function(frm, cdt, cdn) {
        calculate_row_amount(frm, cdt, cdn);
    },

    total_hajri: function(frm, cdt, cdn) {
        calculate_row_amount(frm, cdt, cdn);
    },

    amount: function(frm, cdt, cdn) {
        calculate_row_net_payable(frm, cdt, cdn);
    },

    advances: function(frm, cdt, cdn) {
        calculate_row_net_payable(frm, cdt, cdn);
    },

    net_payable: function(frm, cdt, cdn) {
        frm.trigger("calculate_totals");
    },

    table_xwtx_add: function(frm, cdt, cdn) {
        frm.trigger("calculate_totals");
    },

    table_xwtx_remove: function(frm, cdt, cdn) {
        frm.trigger("calculate_totals");
    }
});

function calculate_row_amount(frm, cdt, cdn) {
    let row = locals[cdt][cdn];
    let amount = flt(row.rate || 0) * flt(row.total_hajri || 0);
    frappe.model.set_value(cdt, cdn, 'amount', amount);
    calculate_row_net_payable(frm, cdt, cdn);
}

function calculate_row_net_payable(frm, cdt, cdn) {
    let row = locals[cdt][cdn];
    let net_payable = flt(row.amount || 0) - flt(row.advances || 0);
    frappe.model.set_value(cdt, cdn, 'net_payable', net_payable);
    frm.trigger("calculate_totals");
}
