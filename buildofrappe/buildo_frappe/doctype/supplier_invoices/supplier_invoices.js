// Copyright (c) 2025, <PERSON><PERSON> and contributors
// For license information, please see license.txt

frappe.ui.form.on("Supplier Invoices", {
    refresh: function(frm) {
        calculate_totals(frm);

        // Add payment allocation buttons
        if (!frm.doc.__islocal) {
            frm.add_custom_button(__("View Payment Allocations"), function() {
                show_invoice_payment_allocations(frm);
            }, __("Payment Allocation"));
        }

        // Show balance amount indicator
        if (frm.doc.balance_amount && frm.doc.balance_amount > 0) {
            frm.dashboard.add_indicator(__("Outstanding: {0}", [format_currency(frm.doc.balance_amount)]), "orange");
        } else if (frm.doc.balance_amount === 0 && frm.doc.grand_total > 0) {
            frm.dashboard.add_indicator(__("Fully Paid"), "green");
        }
    },
    validate: function(frm) {
        calculate_totals(frm);
    },
    cgst_perc: function(frm) {
        calculate_totals(frm);
    },
    sgst_perc: function(frm) {
        calculate_totals(frm);
    },
    igst_perc: function(frm) {
        calculate_totals(frm);
    },
    tcs_amount: function(frm) {
        calculate_totals(frm);
    }
});

frappe.ui.form.on("supplier item table", {
    qty: function(frm, cdt, cdn) {
        calculate_amount(frm, cdt, cdn);
    },
    rate: function(frm, cdt, cdn) {
        calculate_amount(frm, cdt, cdn);
    },
    amount: function(frm, cdt, cdn) {
        calculate_totals(frm);
    },
    // This event fires when a new row is added to the child table
    table_wkfs_add: function(frm, cdt, cdn) {
        calculate_amount(frm, cdt, cdn);
    },
    table_wkfs_remove: function(frm, cdt, cdn) {
        calculate_totals(frm);
    }
});

function calculate_amount(frm, cdt, cdn) {
    var row = frappe.get_doc(cdt, cdn);
    var qty = flt(row.qty);
    var rate = flt(row.rate);
    row.amount = qty * rate;
    frm.refresh_field("table_wkfs"); // Refresh the entire child table field
    calculate_totals(frm); // Recalculate totals after individual row amount changes
}

function calculate_totals(frm) {
    var sub_total = 0;
    if (frm.doc.table_wkfs) {
        frm.doc.table_wkfs.forEach(function(row) {
            sub_total += flt(row.amount);
        });
    }
    frm.set_value("sub_total", sub_total);

    var cgst_perc = flt(frm.doc.cgst_perc);
    var sgst_perc = flt(frm.doc.sgst_perc);
    var igst_perc = flt(frm.doc.igst_perc);
    var tcs_amount = flt(frm.doc.tcs_amount);

    var cgst_amount = (cgst_perc * sub_total) / 100;
    var sgst_amount = (sgst_perc * sub_total) / 100;
    var igst_amount = (igst_perc * sub_total) / 100;

    frm.set_value("cgst_amount", cgst_amount);
    frm.set_value("sgst_amount", sgst_amount);
    frm.set_value("igst_amount", igst_amount);

    var grand_total = sub_total + cgst_amount + sgst_amount + igst_amount + tcs_amount;
    frm.set_value("grand_total", grand_total);

    frm.refresh_fields(["sub_total", "cgst_amount", "sgst_amount", "igst_amount", "grand_total"]);
}

// Function to show payment allocations for this invoice
function show_invoice_payment_allocations(frm) {
	frappe.call({
		method: "frappe.client.get_list",
		args: {
			doctype: "Supplier Payment Allocation",
			filters: {
				supplier_invoice: frm.doc.name
			},
			fields: ["payments_paid", "allocated_amount", "allocation_date", "allocation_method", "remarks"]
		},
		callback: function(r) {
			if (r.message && r.message.length > 0) {
				let total_allocated = r.message.reduce((sum, alloc) => sum + (alloc.allocated_amount || 0), 0);

				let html = `
					<div class="allocation-summary">
						<h4>Payment Allocation Summary</h4>
						<p><strong>Invoice Total:</strong> ${format_currency(frm.doc.grand_total)}</p>
						<p><strong>Total Allocated:</strong> ${format_currency(total_allocated)}</p>
						<p><strong>Outstanding Balance:</strong> ${format_currency(frm.doc.balance_amount || 0)}</p>
						<hr>
						<table class="table table-bordered">
							<thead>
								<tr>
									<th>Payment</th>
									<th>Allocated Amount</th>
									<th>Allocation Date</th>
									<th>Method</th>
								</tr>
							</thead>
							<tbody>
				`;

				r.message.forEach(function(alloc) {
					html += `
						<tr>
							<td><a href="/app/payments-paid/${alloc.payments_paid}">${alloc.payments_paid}</a></td>
							<td>${format_currency(alloc.allocated_amount)}</td>
							<td>${frappe.datetime.str_to_user(alloc.allocation_date)}</td>
							<td>${alloc.allocation_method}</td>
						</tr>
					`;
				});

				html += `
							</tbody>
						</table>
					</div>
				`;

				frappe.msgprint({
					title: __("Payment Allocation Summary"),
					message: html,
					wide: true
				});
			} else {
				frappe.msgprint(__("No payment allocations found for this invoice."));
			}
		}
	});
}
