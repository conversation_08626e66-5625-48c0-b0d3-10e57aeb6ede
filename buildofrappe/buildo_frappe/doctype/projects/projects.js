// Copyright (c) 2025, <PERSON><PERSON> and contributors
// For license information, please see license.txt

frappe.ui.form.on("Projects", {
    refresh: function() {
        // Progress and total value are calculated automatically on save
        // No need for manual calculation on refresh
    },

    contract_type: function(frm) {
        // When contract type changes, trigger total value calculation
        if (frm.doc.contract_type === "Built Up") {
            // For Built Up, calculate total value as area * rate
            frm.trigger('calculate_built_up_total_value');
        } else if (frm.doc.contract_type === "Item Rate") {
            // For Item Rate, total value will be calculated from BOQ grand totals on save
            // Set a message to inform user
            frappe.msgprint(__("Total value will be calculated from BOQ grand totals when you save the project."));
        }
    },

    area: function(frm) {
        if (frm.doc.contract_type === "Built Up") {
            frm.trigger('calculate_built_up_total_value');
        }
    },

    rate: function(frm) {
        if (frm.doc.contract_type === "Built Up") {
            frm.trigger('calculate_built_up_total_value');
        }
    },

    calculate_built_up_total_value: function(frm) {
        if (frm.doc.contract_type === "Built Up" && frm.doc.area && frm.doc.rate) {
            frm.set_value('total_value', frm.doc.area * frm.doc.rate);
        } else if (frm.doc.contract_type === "Built Up") {
            frm.set_value('total_value', 0);
        }
    }
});
