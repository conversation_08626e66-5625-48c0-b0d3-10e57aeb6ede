{"actions": [], "allow_rename": 1, "autoname": "field:project_name", "creation": "2025-07-28 22:33:47.601707", "default_view": "List", "doctype": "DocType", "engine": "InnoDB", "field_order": ["project_name", "customer", "address", "area", "rate", "total_value", "column_break_jovc", "start_date", "end_date", "contract_type", "progress_"], "fields": [{"fieldname": "project_name", "fieldtype": "Data", "label": "Project Name", "unique": 1}, {"fieldname": "customer", "fieldtype": "Link", "label": "Customer", "options": "Customers"}, {"fieldname": "address", "fieldtype": "Small Text", "label": "Address"}, {"fieldname": "area", "fieldtype": "Float", "label": "Area"}, {"fieldname": "rate", "fieldtype": "Float", "label": "Rate"}, {"fieldname": "total_value", "fieldtype": "Float", "label": "Total Value", "read_only": 1}, {"fieldname": "column_break_jovc", "fieldtype": "Column Break"}, {"fieldname": "start_date", "fieldtype": "Date", "label": "Start Date"}, {"fieldname": "end_date", "fieldtype": "Date", "label": "End Date"}, {"fieldname": "contract_type", "fieldtype": "Select", "label": "Contract Type", "options": "Built Up\nItem Rate"}, {"fieldname": "progress_", "fieldtype": "Percent", "label": "Progress %", "precision": "2"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-09-20 14:10:53.769814", "modified_by": "Administrator", "module": "Buildo Frappe", "name": "Projects", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}