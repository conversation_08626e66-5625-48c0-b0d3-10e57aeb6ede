{"actions": [], "allow_rename": 1, "creation": "2025-09-20 13:12:09.151108", "doctype": "DocType", "engine": "InnoDB", "field_order": ["test"], "fields": [{"fieldname": "test", "fieldtype": "Data", "label": "Test"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-09-20 13:12:19.095221", "modified_by": "Administrator", "module": "Buildo Frappe", "name": "Test", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}