{"actions": [], "allow_rename": 1, "creation": "2025-07-28 22:42:46.979556", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["invoice", "invoice_date", "invoice_total_amount", "outstanding_amount_before_adjustment", "adjusted_amount", "remaining_balance_amount"], "fields": [{"fieldname": "invoice", "fieldtype": "Link", "label": "Invoice", "options": "Client Invoice", "read_only": 1}, {"fieldname": "invoice_date", "fieldtype": "Date", "label": "Invoice Date", "read_only": 1}, {"fieldname": "invoice_total_amount", "fieldtype": "Float", "label": "Invoice Total Amount", "precision": "2", "read_only": 1}, {"fieldname": "outstanding_amount_before_adjustment", "fieldtype": "Float", "label": "Outstanding Amount Before Adjustment", "precision": "2", "read_only": 1}, {"fieldname": "adjusted_amount", "fieldtype": "Float", "label": "Adjusted Amount", "precision": "2", "read_only": 1}, {"fieldname": "remaining_balance_amount", "fieldtype": "Float", "label": "Remaining Balance Amount", "precision": "2", "read_only": 1}], "grid_page_length": 50, "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2025-09-20 16:10:16.654656", "modified_by": "Administrator", "module": "Buildo Frappe", "name": "Payment Received Invoice Adjustment", "owner": "Administrator", "permissions": [], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}