{"actions": [], "allow_rename": 1, "autoname": "format:CHKLST{project}-{part}-{#######}", "creation": "2025-09-16 17:17:12.639783", "doctype": "DocType", "engine": "InnoDB", "field_order": ["date", "location", "ref_drwg_no", "part", "column_break_aeoy", "client", "contractor", "project", "checklist_for", "drawing_details_section", "service_plan", "layout_plan", "elec", "column_break_vfsp", "rcc_plan", "arch_centerline_plan", "plumbing", "drawing_details_slab", "service_plan_slab", "layout_plan_slab", "elec_slab", "column_break_pmev", "rcc_plan_slab", "arch_centerline_plan_slab", "plumbing_slab", "check_following_points_section", "hfl_ref_point", "level", "shuttering", "reinforcement", "column_break_fhtb", "elec_cfp", "plumbing_cfp", "general", "section_break_tcpx", "table_atdf", "rmc_section", "table_agto", "pour_card_section", "table_etah", "section_break_bcqy", "remaks_if_any", "columns_r_wall_pardi_section", "reinforcement_check", "shuttering_check", "raft__footing_section", "table_klvz", "section_break_agqx", "table_lmqu", "reinforcement_section", "table_qvcj", "shabad_section", "table_angy", "plum_concrete_section", "table_kspu", "section_break_nqjq", "con_date", "con_name", "con_designation", "con_sign", "column_break_utiw", "pe_date", "pe_name", "pe_designation", "pe_sign", "column_break_ycsb", "pmc_date", "pmc_name", "pmc_designation", "pmc_sign", "column_break_esbl", "qlt_date", "qlt_name", "qlt_designation", "qlt_sign"], "fields": [{"fieldname": "date", "fieldtype": "Date", "label": "Date"}, {"fieldname": "location", "fieldtype": "Data", "label": "Location"}, {"fieldname": "ref_drwg_no", "fieldtype": "Data", "label": "Ref Drwg No"}, {"fieldname": "part", "fieldtype": "Data", "label": "Part"}, {"fieldname": "column_break_aeoy", "fieldtype": "Column Break"}, {"fieldname": "client", "fieldtype": "Link", "label": "Client", "options": "Customers"}, {"fieldname": "contractor", "fieldtype": "Data", "label": "Contractor"}, {"fieldname": "project", "fieldtype": "Link", "label": "Project", "options": "Projects", "unique": 1}, {"depends_on": "eval:doc.checklist_for == \"Raft / Footing\"", "fieldname": "drawing_details_section", "fieldtype": "Section Break", "label": "Drawing Details"}, {"fieldname": "service_plan", "fieldtype": "Data", "label": "Service Plan"}, {"fieldname": "layout_plan", "fieldtype": "Data", "label": "Layout Plan"}, {"fieldname": "elec", "fieldtype": "Data", "label": "Elec"}, {"fieldname": "column_break_vfsp", "fieldtype": "Column Break"}, {"fieldname": "rcc_plan", "fieldtype": "Data", "label": "RCC Plan"}, {"fieldname": "arch_centerline_plan", "fieldtype": "Data", "label": "Arch Centerline Plan"}, {"fieldname": "plumbing", "fieldtype": "Data", "label": "Plumbing"}, {"fieldname": "checklist_for", "fieldtype": "Select", "label": "Checklist for", "options": "\nShuttering\nRMC\nPour Card\nColumns, R Wall & Pardi\nRaft / Footing\nSlab\nReinforcement\nShabad Waterproofing\nPlum Concrete"}, {"depends_on": "eval:doc.checklist_for == \"Slab\"", "fieldname": "check_following_points_section", "fieldtype": "Section Break", "label": "Check Following Points"}, {"fieldname": "hfl_ref_point", "fieldtype": "Data", "label": "HFL Ref Point"}, {"fieldname": "level", "fieldtype": "Data", "label": "Level"}, {"fieldname": "shuttering", "fieldtype": "Data", "label": "Shuttering"}, {"fieldname": "reinforcement", "fieldtype": "Data", "label": "Reinforcement"}, {"fieldname": "column_break_fhtb", "fieldtype": "Column Break"}, {"fieldname": "elec_cfp", "fieldtype": "Data", "label": "Elec"}, {"fieldname": "plumbing_cfp", "fieldtype": "Data", "label": "Plumbing"}, {"fieldname": "general", "fieldtype": "Data", "label": "General"}, {"depends_on": "eval:doc.checklist_for == \"Shuttering\"", "fieldname": "section_break_tcpx", "fieldtype": "Section Break", "label": "Shuttering"}, {"fieldname": "table_atdf", "fieldtype": "Table", "options": "Checklist_child_shuttering"}, {"depends_on": "eval:doc.checklist_for == \"RMC\"", "fieldname": "rmc_section", "fieldtype": "Section Break", "label": "RMC"}, {"fieldname": "table_agto", "fieldtype": "Table", "options": "Checklist_child_rmc"}, {"depends_on": "eval:doc.checklist_for == \"Pour Card\"", "fieldname": "pour_card_section", "fieldtype": "Section Break", "label": "Pour Card"}, {"fieldname": "table_etah", "fieldtype": "Table", "options": "Checklist_child_pourcard"}, {"depends_on": "eval:doc.checklist_for == \"Pour Card\"", "fieldname": "section_break_bcqy", "fieldtype": "Section Break", "label": "Remarks if any"}, {"fieldname": "remaks_if_any", "fieldtype": "Long Text"}, {"depends_on": "eval:doc.checklist_for == \"Columns, R Wall & Pardi\"", "fieldname": "columns_r_wall_pardi_section", "fieldtype": "Section Break", "label": "Columns, R Wall, Pardi"}, {"fieldname": "reinforcement_check", "fieldtype": "Table", "label": "Reinforcement Check", "options": "Checklist_child_columns_reinf"}, {"fieldname": "shuttering_check", "fieldtype": "Table", "label": "Shuttering Check", "options": "Checklist_child_column_shut"}, {"depends_on": "eval:doc.checklist_for == \"Raft / Footing\"", "fieldname": "raft__footing_section", "fieldtype": "Section Break", "label": "Raft / Footing"}, {"fieldname": "table_klvz", "fieldtype": "Table", "options": "Checklist_child_raft_footing"}, {"depends_on": "eval:doc.checklist_for == \"Slab\"", "fieldname": "section_break_agqx", "fieldtype": "Section Break", "label": "Slab"}, {"fieldname": "table_lmqu", "fieldtype": "Table", "options": "Checklist_child_Slab"}, {"depends_on": "eval:doc.checklist_for == \"Slab\"", "fieldname": "drawing_details_slab", "fieldtype": "Section Break", "label": "Drawing Details"}, {"fieldname": "service_plan_slab", "fieldtype": "Data", "label": "Service Plan"}, {"fieldname": "layout_plan_slab", "fieldtype": "Data", "label": "Layout Plan"}, {"fieldname": "elec_slab", "fieldtype": "Data", "label": "Elec"}, {"fieldname": "column_break_pmev", "fieldtype": "Column Break"}, {"fieldname": "rcc_plan_slab", "fieldtype": "Data", "label": "RCC Plan"}, {"fieldname": "arch_centerline_plan_slab", "fieldtype": "Data", "label": "Arch Centerline Plan"}, {"fieldname": "plumbing_slab", "fieldtype": "Data", "label": "Plumbing"}, {"depends_on": "eval:doc.checklist_for == \"Reinforcement\"", "fieldname": "reinforcement_section", "fieldtype": "Section Break", "label": "Reinforcement"}, {"fieldname": "table_qvcj", "fieldtype": "Table", "options": "Checklist_child_reinf"}, {"depends_on": "eval:doc.checklist_for == \"Shabad Waterproofing\"", "fieldname": "shabad_section", "fieldtype": "Section Break", "label": "<PERSON><PERSON><PERSON>"}, {"fieldname": "table_angy", "fieldtype": "Table", "options": "Checklist_child_shabad"}, {"depends_on": "eval:doc.checklist_for == \"Plum Concrete\"", "fieldname": "plum_concrete_section", "fieldtype": "Section Break", "label": "Plum Concrete"}, {"fieldname": "table_kspu", "fieldtype": "Table", "options": "Checklist_child_plum"}, {"fieldname": "section_break_nqjq", "fieldtype": "Section Break"}, {"fieldname": "con_date", "fieldtype": "Date", "label": "Date"}, {"fieldname": "con_name", "fieldtype": "Data", "label": "Contractor"}, {"fieldname": "con_designation", "fieldtype": "Data", "label": "Designation"}, {"fieldname": "con_sign", "fieldtype": "Data", "label": "Sign"}, {"fieldname": "column_break_utiw", "fieldtype": "Column Break"}, {"fieldname": "pe_date", "fieldtype": "Date", "label": "Date"}, {"fieldname": "pe_name", "fieldtype": "Data", "label": "<PERSON><PERSON>"}, {"fieldname": "pe_designation", "fieldtype": "Data", "label": "Designation"}, {"fieldname": "pe_sign", "fieldtype": "Data", "label": "Sign"}, {"fieldname": "column_break_ycsb", "fieldtype": "Column Break"}, {"fieldname": "pmc_date", "fieldtype": "Date", "label": "Date"}, {"fieldname": "pmc_name", "fieldtype": "Data", "label": "PMC"}, {"fieldname": "pmc_designation", "fieldtype": "Data", "label": "Designation"}, {"fieldname": "pmc_sign", "fieldtype": "Data", "label": "Sign"}, {"fieldname": "column_break_esbl", "fieldtype": "Column Break"}, {"fieldname": "qlt_date", "fieldtype": "Date", "label": "Date"}, {"fieldname": "qlt_name", "fieldtype": "Data", "label": "<PERSON><PERSON><PERSON>"}, {"fieldname": "qlt_designation", "fieldtype": "Data", "label": "Designation"}, {"fieldname": "qlt_sign", "fieldtype": "Data", "label": "Sign"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-09-22 13:33:58.871968", "modified_by": "Administrator", "module": "Buildo Frappe", "name": "Checklist", "naming_rule": "Expression", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}