# Copyright (c) 2025, <PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document
from frappe.utils import flt


class PaymentReceived(Document):
	def validate(self):
		"""Validate payment data before saving"""
		# Initialize unallocated amount
		if not self.unallocated_amount:
			self.unallocated_amount = self.amount or 0

	def after_insert(self):
		"""Automatically allocate payment after insertion"""
		if self.amount and self.customer and self.project:
			self.allocate_payment()

	def on_update(self):
		"""Handle payment updates"""
		# Check if key fields changed and we're not in allocation process
		if self._has_key_fields_changed() and not getattr(self, '_in_allocation', False):
			old_doc = self._doc_before_save
			old_amount = flt(old_doc.amount) if old_doc else 0
			new_amount = flt(self.amount)

			frappe.msgprint(f"Payment amount changed from {old_amount} to {new_amount}. Reallocating payment...")
			self.allocate_payment()

	def _has_key_fields_changed(self):
		"""Check if amount, customer, or project changed"""
		if not self._doc_before_save:
			return False

		old_doc = self._doc_before_save
		amount_changed = abs(flt(self.amount) - flt(old_doc.amount)) > 0.01
		customer_changed = self.customer != old_doc.customer
		project_changed = self.project != old_doc.project

		return amount_changed or customer_changed or project_changed

	def allocate_payment(self):
		"""Main allocation method"""
		try:
			# Set flag to prevent infinite loop
			self._in_allocation = True

			# Clear existing allocations
			self._clear_existing_allocations()

			# Find outstanding invoices
			invoices = self._get_outstanding_invoices()

			if not invoices:
				# Update unallocated amount
				self.unallocated_amount = self.amount
				frappe.db.set_value("Payment Received", self.name, "unallocated_amount", self.amount)
				frappe.msgprint(f"No outstanding invoices found. Full amount remains unallocated: {self.amount}")
				return

			# Perform FIFO allocation
			remaining_amount = flt(self.amount)
			allocation_details = []
			total_allocated = 0

			for invoice in invoices:
				if remaining_amount <= 0:
					break

				invoice_balance = flt(invoice.balance_amount)
				if invoice_balance <= 0:
					continue

				# Calculate allocation amount
				allocation_amount = min(remaining_amount, invoice_balance)

				# Create allocation record
				allocation = frappe.get_doc({
					"doctype": "Payment Allocation",
					"payment_received": self.name,
					"client_invoice": invoice.name,
					"allocated_amount": allocation_amount,
					"customer": self.customer,
					"project": self.project,
					"allocation_date": self.date
				})
				allocation.insert(ignore_permissions=True)

				# Update invoice balance
				new_balance = invoice_balance - allocation_amount
				frappe.db.set_value("Client Invoice", invoice.name, "balance_amount", new_balance)

				# Update invoice status
				self._update_invoice_status(invoice.name, new_balance, flt(invoice.net_payable) - flt(invoice.tds_amount))

				# Add to allocation details
				allocation_details.append({
					"client_invoice": invoice.name,
					"invoice_date": invoice.date,
					"invoice_amount": invoice.net_payable,
					"allocated_amount": allocation_amount,
					"invoice_balance_before": invoice_balance,
					"invoice_balance_after": new_balance
				})

				remaining_amount -= allocation_amount
				total_allocated += allocation_amount

			# Update unallocated amount
			self.unallocated_amount = remaining_amount
			frappe.db.set_value("Payment Received", self.name, "unallocated_amount", remaining_amount)

			# Update allocation details child table
			self.allocation_details = []
			for detail in allocation_details:
				self.append("allocation_details", detail)

			# Save allocation details without triggering hooks
			if allocation_details:
				frappe.db.sql("""
					DELETE FROM `tabPayment Allocation Detail`
					WHERE parent = %s
				""", self.name)

				for detail in allocation_details:
					frappe.db.sql("""
						INSERT INTO `tabPayment Allocation Detail`
						(name, parent, parenttype, parentfield, client_invoice, invoice_date,
						 invoice_amount, allocated_amount, invoice_balance_before, invoice_balance_after)
						VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
					""", (
						frappe.generate_hash(length=10),
						self.name,
						"Payment Received",
						"allocation_details",
						detail["client_invoice"],
						detail["invoice_date"],
						detail["invoice_amount"],
						detail["allocated_amount"],
						detail["invoice_balance_before"],
						detail["invoice_balance_after"]
					))

			frappe.db.commit()
			frappe.msgprint(f"Payment allocated successfully. Allocated: {total_allocated}, Unallocated: {remaining_amount}")

		except Exception as e:
			frappe.db.rollback()
			frappe.log_error(f"Error in payment allocation: {str(e)}", "Payment Allocation Error")
			frappe.throw(f"Error in payment allocation: {str(e)}")
		finally:
			# Reset flag
			self._in_allocation = False

	def _get_outstanding_invoices(self):
		"""Get outstanding invoices for FIFO allocation"""
		return frappe.get_all(
			"Client Invoice",
			filters={
				"customer": self.customer,
				"project": self.project,
				"balance_amount": [">", 0],
				"docstatus": ["in", [0, 1]]  # Allow both draft (0) and submitted (1) invoices
			},
			fields=["name", "date", "net_payable", "tds_amount", "balance_amount"],
			order_by="date asc"
		)

	def _clear_existing_allocations(self):
		"""Clear existing allocations and restore invoice balances"""
		allocations = frappe.get_all(
			"Payment Allocation",
			filters={"payment_received": self.name},
			fields=["name", "client_invoice", "allocated_amount"]
		)

		if allocations:
			total_restored = 0
			restored_invoices = []

			for allocation in allocations:
				# Restore invoice balance
				current_balance = frappe.db.get_value("Client Invoice", allocation.client_invoice, "balance_amount")
				new_balance = flt(current_balance) + flt(allocation.allocated_amount)
				frappe.db.set_value("Client Invoice", allocation.client_invoice, "balance_amount", new_balance)

				# Update invoice status
				invoice_data = frappe.db.get_value("Client Invoice", allocation.client_invoice,
												 ["net_payable", "tds_amount"], as_dict=True)
				if invoice_data:
					net_payable = flt(invoice_data.net_payable) - flt(invoice_data.tds_amount)
					self._update_invoice_status(allocation.client_invoice, new_balance, net_payable)

				# Track restoration details
				total_restored += flt(allocation.allocated_amount)
				restored_invoices.append(f"{allocation.client_invoice} (+{allocation.allocated_amount})")

				# Delete allocation record
				frappe.delete_doc("Payment Allocation", allocation.name, ignore_permissions=True)

			# Clear allocation details from child table
			frappe.db.sql("DELETE FROM `tabPayment Allocation Detail` WHERE parent = %s", self.name)

			# Log restoration details
			frappe.logger().info(f"Cleared {len(allocations)} allocations for payment {self.name}. "
							   f"Restored total amount: {total_restored}. "
							   f"Affected invoices: {', '.join(restored_invoices)}")
		else:
			frappe.logger().info(f"No existing allocations found for payment {self.name}")

	def _update_invoice_status(self, invoice_name, balance_amount, net_payable):
		"""Update invoice status based on balance"""
		if balance_amount <= 0:
			status = "Fully Paid"
		elif balance_amount >= net_payable:
			status = "Not Paid"
		else:
			status = "Partially Paid"

		frappe.db.set_value("Client Invoice", invoice_name, "status", status)

	def before_delete(self):
		"""Restore invoice balances before deletion"""
		self._clear_existing_allocations()
