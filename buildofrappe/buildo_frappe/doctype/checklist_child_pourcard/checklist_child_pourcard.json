{"actions": [], "allow_rename": 1, "creation": "2025-09-16 17:45:06.344405", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["particulars", "details"], "fields": [{"fieldname": "particulars", "fieldtype": "Data", "in_list_view": 1, "label": "Particulars"}, {"fieldname": "details", "fieldtype": "Data", "in_list_view": 1, "label": "Details"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2025-09-22 11:39:38.740029", "modified_by": "Administrator", "module": "Buildo Frappe", "name": "Checklist_child_pourcard", "owner": "Administrator", "permissions": [], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}