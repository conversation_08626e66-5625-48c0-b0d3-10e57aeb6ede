{"actions": [], "allow_rename": 1, "autoname": "format:{month}-{year}-{#####}", "creation": "2025-06-19 16:02:29.837739", "doctype": "DocType", "engine": "InnoDB", "field_order": ["month", "column_break_rtsm", "year", "section_break_mjyb", "table_junc", "section_break_ttxz", "total_salary", "column_break_qnlc", "total_allowance", "column_break_yyrn", "total_deduction", "column_break_fnwk", "total_net_payable"], "fields": [{"fieldname": "month", "fieldtype": "Data", "label": "Month"}, {"fieldname": "column_break_rtsm", "fieldtype": "Column Break"}, {"fieldname": "year", "fieldtype": "Data", "label": "Year"}, {"fieldname": "section_break_mjyb", "fieldtype": "Section Break"}, {"fieldname": "table_junc", "fieldtype": "Table", "options": "salary table"}, {"fieldname": "section_break_ttxz", "fieldtype": "Section Break"}, {"fieldname": "total_salary", "fieldtype": "Float", "label": "Total Salary"}, {"fieldname": "column_break_qnlc", "fieldtype": "Column Break"}, {"fieldname": "total_allowance", "fieldtype": "Float", "label": "Total Allowance"}, {"fieldname": "column_break_yyrn", "fieldtype": "Column Break"}, {"fieldname": "total_deduction", "fieldtype": "Float", "label": "Total Deduction"}, {"fieldname": "column_break_fnwk", "fieldtype": "Column Break"}, {"fieldname": "total_net_payable", "fieldtype": "Float", "label": "Total Net Payable"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-09-20 15:28:30.458349", "modified_by": "Administrator", "module": "Buildo Frappe", "name": "Salary", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}