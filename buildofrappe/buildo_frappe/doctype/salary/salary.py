# Copyright (c) 2025, <PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document

class Salary(Document):
    def validate(self):
        self.calculate_totals()

    def calculate_totals(self):
        self.total_salary = 0
        self.total_allowance = 0
        self.total_deduction = 0
        self.total_net_payable = 0

        for row in self.get("table_junc"):
            self.total_salary += (row.salary or 0)
            self.total_allowance += (row.allowance or 0)
            self.total_deduction += (row.deductions or 0)
            self.total_net_payable += (row.net_salary or 0)

@frappe.whitelist()
def fetch_active_staff():
    staff_members = frappe.get_all(
        "Staff",
        filters={"is_active": 1},
        fields=["staff_name", "salary"]
    )
    return staff_members