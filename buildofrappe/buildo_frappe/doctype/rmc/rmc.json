{"actions": [], "allow_rename": 1, "autoname": "format:{project}-{grade}-{#####}", "creation": "2025-06-19 15:48:58.652473", "doctype": "DocType", "engine": "InnoDB", "field_order": ["project", "column_break_dgbu", "grade", "section_break_fbmc", "table_gaue", "section_break_igzm", "total_qty"], "fields": [{"fieldname": "project", "fieldtype": "Link", "label": "Project", "options": "Projects"}, {"fieldname": "column_break_dgbu", "fieldtype": "Column Break"}, {"fieldname": "grade", "fieldtype": "Select", "label": "Grade", "options": "M10\nM15\nM20\nM25\nM30\nM35\nM40\nM45\nM50\nM55\nM60\nM65\nM70\nM75\nM80\nM85\nM90\nM95\nM100"}, {"fieldname": "section_break_fbmc", "fieldtype": "Section Break"}, {"fieldname": "table_gaue", "fieldtype": "Table", "options": "Rmc table"}, {"fieldname": "section_break_igzm", "fieldtype": "Section Break"}, {"fieldname": "total_qty", "fieldtype": "Float", "label": "Total Qty"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-09-23 15:59:18.670365", "modified_by": "Administrator", "module": "Buildo Frappe", "name": "RMC", "naming_rule": "Expression", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}