// Copyright (c) 2025, <PERSON><PERSON> and contributors
// For license information, please see license.txt

frappe.ui.form.on("DPR", {
	refresh(frm) {
		// Calculate totals when form is refreshed
		calculate_manpower_totals(frm);
	},
});

// Function to calculate totals for all manpower columns
function calculate_manpower_totals(frm) {
	let total_prev = 0;
	let total_today = 0;
	let total_spec = 0;
	let total_carp = 0;
	let total_fitt = 0;
	let total_b_mas = 0;
	let total_p_mas = 0;
	let total_mc = 0;

	// Loop through each row in manpower_table
	frm.doc.manpower_table.forEach(function(row) {
		total_prev += flt(row.prev);
		total_today += flt(row.today);
		total_spec += flt(row.specialized);
		total_carp += flt(row.carp);
		total_fitt += flt(row.fitt);
		total_b_mas += flt(row.b_mas);
		total_p_mas += flt(row.pmas);
		total_mc += flt(row.mc);
	});

	// Set the calculated totals to respective fields
	frm.set_value('total_prev', total_prev);
	frm.set_value('total_today', total_today);
	frm.set_value('total_spec', total_spec);
	frm.set_value('total_carp', total_carp);
	frm.set_value('total_fitt', total_fitt);
	frm.set_value('total_b_mas', total_b_mas);
	frm.set_value('total_p_mas', total_p_mas);
	frm.set_value('total_mc', total_mc);
}

// Event handler for manpower_table changes
frappe.ui.form.on("Manpower_child_dpr", {
	prev: function(frm, cdt, cdn) {
		calculate_manpower_totals(frm);
	},
	today: function(frm, cdt, cdn) {
		calculate_manpower_totals(frm);
	},
	specialized: function(frm, cdt, cdn) {
		calculate_manpower_totals(frm);
	},
	carp: function(frm, cdt, cdn) {
		calculate_manpower_totals(frm);
	},
	fitt: function(frm, cdt, cdn) {
		calculate_manpower_totals(frm);
	},
	b_mas: function(frm, cdt, cdn) {
		calculate_manpower_totals(frm);
	},
	pmas: function(frm, cdt, cdn) {
		calculate_manpower_totals(frm);
	},
	mc: function(frm, cdt, cdn) {
		calculate_manpower_totals(frm);
	},
	manpower_table_add: function(frm, cdt, cdn) {
		calculate_manpower_totals(frm);
	},
	manpower_table_remove: function(frm, cdt, cdn) {
		calculate_manpower_totals(frm);
	}
});
