{"actions": [], "allow_rename": 1, "autoname": "format:DPR-{date}-{project}-{#######}", "creation": "2025-09-22 13:55:50.438504", "doctype": "DocType", "engine": "InnoDB", "field_order": ["project", "client", "address", "column_break_acyr", "date", "total_days", "days_elapsed", "balance", "manpower_report_section", "manpower_table", "section_break_ftgh", "total_prev", "column_break_vxck", "total_today", "column_break_mcjw", "total_spec", "column_break_vcmp", "total_carp", "column_break_tecl", "total_fitt", "column_break_hjwl", "total_b_mas", "column_break_gyis", "total_p_mas", "column_break_gdrw", "total_mc", "work_status_section", "wdone_today_table", "column_break_mprn", "wplanned_tomm_table", "section_break_ndnf", "hold_ups_stoppages_of_works_with_reasons", "section_break_jirc", "request_for_information", "section_break_tpbk", "mach_deployed_table", "section_break_fmxd", "status_of_material", "today_material_receipt_section", "mat_recp_table"], "fields": [{"fieldname": "project", "fieldtype": "Link", "label": "Project", "options": "Projects"}, {"fieldname": "client", "fieldtype": "Link", "label": "Client ", "options": "Customers"}, {"fieldname": "address", "fieldtype": "Data", "label": "Address"}, {"fieldname": "column_break_acyr", "fieldtype": "Column Break"}, {"fieldname": "total_days", "fieldtype": "Data", "label": "Total Days"}, {"fieldname": "days_elapsed", "fieldtype": "Data", "label": "Days Elapsed"}, {"fieldname": "balance", "fieldtype": "Data", "label": "Balance"}, {"fieldname": "manpower_report_section", "fieldtype": "Section Break", "label": "Manpower Report"}, {"fieldname": "work_status_section", "fieldtype": "Section Break", "label": "Work Status"}, {"fieldname": "column_break_mprn", "fieldtype": "Column Break"}, {"fieldname": "section_break_ndnf", "fieldtype": "Section Break"}, {"fieldname": "hold_ups_stoppages_of_works_with_reasons", "fieldtype": "Long Text", "label": "Hold Ups /Stoppages of works with reasons"}, {"fieldname": "section_break_jirc", "fieldtype": "Section Break"}, {"fieldname": "request_for_information", "fieldtype": "Long Text", "label": " Request for information "}, {"fieldname": "section_break_tpbk", "fieldtype": "Section Break", "label": "Equipment / Machinery Deployed"}, {"fieldname": "section_break_fmxd", "fieldtype": "Section Break"}, {"fieldname": "status_of_material", "fieldtype": "Text Editor", "label": "Status of Material"}, {"fieldname": "today_material_receipt_section", "fieldtype": "Section Break", "label": "Today Material Receipt"}, {"fieldname": "date", "fieldtype": "Date", "label": "Date"}, {"fieldname": "section_break_ftgh", "fieldtype": "Section Break"}, {"fieldname": "total_prev", "fieldtype": "Float", "label": "Total Prev", "read_only": 1}, {"fieldname": "total_today", "fieldtype": "Float", "label": "Total Today", "read_only": 1}, {"fieldname": "total_spec", "fieldtype": "Float", "label": "Total Spec", "read_only": 1}, {"fieldname": "total_carp", "fieldtype": "Float", "label": "Total Carp", "read_only": 1}, {"fieldname": "total_fitt", "fieldtype": "Float", "label": "Total Fitt", "read_only": 1}, {"fieldname": "total_b_mas", "fieldtype": "Float", "label": "Total B Mas", "read_only": 1}, {"fieldname": "total_p_mas", "fieldtype": "Float", "label": "Total P Mas", "read_only": 1}, {"fieldname": "total_mc", "fieldtype": "Float", "label": "Total MC", "read_only": 1}, {"fieldname": "column_break_mcjw", "fieldtype": "Column Break"}, {"fieldname": "column_break_tecl", "fieldtype": "Column Break"}, {"fieldname": "column_break_gyis", "fieldtype": "Column Break"}, {"fieldname": "column_break_vxck", "fieldtype": "Column Break"}, {"fieldname": "column_break_vcmp", "fieldtype": "Column Break"}, {"fieldname": "column_break_hjwl", "fieldtype": "Column Break"}, {"fieldname": "column_break_gdrw", "fieldtype": "Column Break"}, {"fieldname": "manpower_table", "fieldtype": "Table", "options": "Manpower_child_dpr"}, {"fieldname": "wdone_today_table", "fieldtype": "Table", "options": "Workstats_today_child_dpr"}, {"fieldname": "wplanned_tomm_table", "fieldtype": "Table", "options": "workstats_tom_child_dpr"}, {"fieldname": "mach_deployed_table", "fieldtype": "Table", "options": "Machine_chld_dpr"}, {"fieldname": "mat_recp_table", "fieldtype": "Table", "options": "Materialrcpt_child_dpr"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-09-22 15:12:19.629107", "modified_by": "Administrator", "module": "Buildo Frappe", "name": "DPR", "naming_rule": "Expression (old style)", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}