// Copyright (c) 2025, <PERSON><PERSON> and contributors
// For license information, please see license.txt

frappe.query_reports["Customer Ledgers"] = {
	"filters": [
		{
			"fieldname": "customer",
			"label": __("Customer"),
			"fieldtype": "Link",
			"options": "Customers",
			"width": "100px",
			"reqd": 0
		},
		{
			"fieldname": "project",
			"label": __("Project"),
			"fieldtype": "Link",
			"options": "Projects",
			"width": "100px",
			"reqd": 0
		},
		{
			"fieldname": "from_date",
			"label": __("From Date"),
			"fieldtype": "Date",
			"default": frappe.datetime.add_months(frappe.datetime.get_today(), -1),
			"width": "100px"
		},
		{
			"fieldname": "to_date",
			"label": __("To Date"),
			"fieldtype": "Date",
			"default": frappe.datetime.get_today(),
			"width": "100px"
		}
	],

	"formatter": function(value, row, column, data, default_formatter) {
		// Apply color formatting to specific columns
		if (column.fieldname === "debit" && value) {
			return `<span style="color: red; font-weight: bold;">${default_formatter(value, row, column, data)}</span>`;
		}
		if (column.fieldname === "credit" && value) {
			return `<span style="color: green; font-weight: bold;">${default_formatter(value, row, column, data)}</span>`;
		}
		if (column.fieldname === "balance") {
			const color = parseFloat(value) < 0 ? "red" : "blue";
			return `<span style="color: ${color}; font-weight: bold;">${default_formatter(value, row, column, data)}</span>`;
		}
		return default_formatter(value, row, column, data);
	}
};
