# Copyright (c) 2025, <PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe import _
from frappe.utils import flt, getdate, formatdate


def execute(filters=None):
	"""
	Main execution function for Customer Ledgers report

	Args:
		filters (dict): Report filters containing project, customer, from_date, to_date

	Returns:
		tuple: (columns, data, message, chart, report_summary) for the report
	"""
	if not filters:
		filters = {}

	# No validation required - show all data if no filters selected

	# Get columns definition
	columns = get_columns(filters)

	# Get report data
	data = get_ledger_data(filters)

	# Calculate summary data for cards
	summary_data = calculate_summary_data(data)

	# Create report summary cards
	report_summary = get_report_summary(summary_data)

	return columns, data, None, None, report_summary


def get_columns(filters=None):
	"""
	Define columns for the Customer Ledgers report

	Args:
		filters (dict): Report filters to determine which columns to show

	Returns:
		list: Column definitions with formatting
	"""
	columns = [
		{
			"label": _("Date"),
			"fieldname": "date",
			"fieldtype": "Date",
			"width": 100
		},
		{
			"label": _("Type"),
			"fieldname": "type",
			"fieldtype": "Data",
			"width": 120
		}
	]

	# Add Customer column if no specific customer is selected
	if not filters or not filters.get("customer"):
		columns.append({
			"label": _("Customer"),
			"fieldname": "customer",
			"fieldtype": "Link",
			"options": "Customers",
			"width": 150
		})

	# Add Project column if no specific project is selected
	if not filters or not filters.get("project"):
		columns.append({
			"label": _("Project"),
			"fieldname": "project",
			"fieldtype": "Link",
			"options": "Projects",
			"width": 150
		})

	# Add remaining columns
	columns.extend([
		{
			"label": _("Reference"),
			"fieldname": "reference",
			"fieldtype": "Data",
			"width": 170
		},
		{
			"label": _("Debit"),
			"fieldname": "debit",
			"fieldtype": "Currency",
			"width": 120
		},
		{
			"label": _("Credit"),
			"fieldname": "credit",
			"fieldtype": "Currency",
			"width": 120
		},
		{
			"label": _("Balance"),
			"fieldname": "balance",
			"fieldtype": "Currency",
			"width": 120
		}
	])

	return columns


def get_ledger_data(filters):
	"""
	Get ledger data combining invoices and payments with running balance

	Args:
		filters (dict): Report filters

	Returns:
		list: Ledger entries with running balance
	"""
	# Get invoice transactions (debits)
	invoice_transactions = get_invoice_transactions(filters)

	# Get payment transactions (credits)
	payment_transactions = get_payment_transactions(filters)

	# Combine and sort all transactions
	all_transactions = invoice_transactions + payment_transactions

	# If specific customer is selected, calculate running balance normally
	if filters and filters.get("customer"):
		all_transactions.sort(key=lambda x: (getdate(x['date']), x['type']))

		running_balance = 0
		ledger_data = []

		for transaction in all_transactions:
			debit = flt(transaction.get('debit', 0))
			credit = flt(transaction.get('credit', 0))

			# Update running balance
			running_balance += debit - credit

			# Add balance to transaction
			transaction['balance'] = running_balance

			ledger_data.append(transaction)

		return ledger_data

	# If no specific customer, group by customer and calculate running balance per customer
	else:
		# Sort by customer, then by date
		all_transactions.sort(key=lambda x: (x.get('customer', ''), getdate(x['date']), x['type']))

		customer_balances = {}
		ledger_data = []

		for transaction in all_transactions:
			customer = transaction.get('customer', '')
			debit = flt(transaction.get('debit', 0))
			credit = flt(transaction.get('credit', 0))

			# Initialize customer balance if not exists
			if customer not in customer_balances:
				customer_balances[customer] = 0

			# Update customer running balance
			customer_balances[customer] += debit - credit

			# Add balance to transaction
			transaction['balance'] = customer_balances[customer]

			ledger_data.append(transaction)

		return ledger_data


def get_invoice_transactions(filters):
	"""
	Get invoice transactions (debit entries) for the customer ledger

	Args:
		filters (dict): Report filters

	Returns:
		list: Invoice transactions with calculated debit amounts
	"""
	conditions = []
	values = []

	# Customer filter (optional)
	if filters.get("customer"):
		conditions.append("customer = %s")
		values.append(filters.get("customer"))

	# Project filter (optional)
	if filters.get("project"):
		conditions.append("project = %s")
		values.append(filters.get("project"))

	# Date range filters
	if filters.get("from_date"):
		conditions.append("date >= %s")
		values.append(filters.get("from_date"))

	if filters.get("to_date"):
		conditions.append("date <= %s")
		values.append(filters.get("to_date"))

	# Only non-cancelled invoices
	conditions.append("docstatus != 2")

	where_clause = " AND ".join(conditions) if conditions else "1=1"

	query = f"""
		SELECT
			date,
			name,
			invoice_no,
			net_payable,
			tds_amount,
			customer,
			project
		FROM `tabClient Invoice`
		WHERE {where_clause}
		ORDER BY date ASC, creation ASC
	"""

	invoices = frappe.db.sql(query, values, as_dict=True)

	transactions = []
	for invoice in invoices:
		# Calculate debit amount (net_payable - tds_amount)
		net_payable = flt(invoice.net_payable)
		tds_amount = flt(invoice.tds_amount)
		debit_amount = net_payable - tds_amount

		# Only include if debit amount is positive
		if debit_amount > 0:
			# Create descriptive reference
			reference = invoice.invoice_no or invoice.name
			if invoice.invoice_no and invoice.invoice_no != invoice.name:
				reference = f"{invoice.invoice_no} ({invoice.name})"

			transactions.append({
				'date': invoice.date,
				'type': 'Invoice',
				'customer': invoice.customer,
				'project': invoice.project,
				'reference': reference,
				'debit': debit_amount,
				'credit': 0,
				'invoice_name': invoice.name
			})

	return transactions


def get_payment_transactions(filters):
	"""
	Get payment transactions (credit entries) for the customer ledger

	Args:
		filters (dict): Report filters

	Returns:
		list: Payment transactions
	"""
	conditions = []
	values = []

	# Customer filter (optional)
	if filters.get("customer"):
		conditions.append("customer = %s")
		values.append(filters.get("customer"))

	# Project filter (optional)
	if filters.get("project"):
		conditions.append("project = %s")
		values.append(filters.get("project"))

	# Date range filters
	if filters.get("from_date"):
		conditions.append("date >= %s")
		values.append(filters.get("from_date"))

	if filters.get("to_date"):
		conditions.append("date <= %s")
		values.append(filters.get("to_date"))

	# Only non-cancelled payments
	conditions.append("docstatus != 2")

	where_clause = " AND ".join(conditions) if conditions else "1=1"

	query = f"""
		SELECT
			date,
			name,
			receipt_no,
			amount,
			customer,
			project,
			payment_mode,
			reference_no
		FROM `tabPayment Received`
		WHERE {where_clause}
		ORDER BY date ASC, creation ASC
	"""

	payments = frappe.db.sql(query, values, as_dict=True)

	transactions = []
	for payment in payments:
		amount = flt(payment.amount)

		# Only include if amount is positive
		if amount > 0:
			reference = payment.receipt_no or payment.name
			if payment.reference_no:
				reference += f" (Ref: {payment.reference_no})"

			# Add payment mode for better identification
			if payment.payment_mode:
				reference += f" - {payment.payment_mode}"

			transactions.append({
				'date': payment.date,
				'type': 'Payment Received',
				'customer': payment.customer,
				'project': payment.project,
				'reference': reference,
				'debit': 0,
				'credit': amount,
				'payment_name': payment.name
			})

	return transactions


def calculate_summary_data(data):
	"""
	Calculate summary data from ledger transactions

	Args:
		data (list): Ledger data with transactions

	Returns:
		dict: Summary data with totals
	"""
	total_invoice_amount = 0
	total_payment_received = 0
	running_balance = 0

	if data:
		for transaction in data:
			debit = flt(transaction.get('debit', 0))
			credit = flt(transaction.get('credit', 0))

			total_invoice_amount += debit
			total_payment_received += credit

			# The last transaction will have the final running balance
			running_balance = flt(transaction.get('balance', 0))

	return {
		'total_invoice_amount': total_invoice_amount,
		'total_payment_received': total_payment_received,
		'running_balance': running_balance
	}


def get_report_summary(summary_data):
	"""
	Create report summary cards for display at the top of the report

	Args:
		summary_data (dict): Summary data with totals

	Returns:
		list: Report summary cards
	"""
	currency = frappe.defaults.get_user_default("currency") or "INR"

	# Determine balance color based on positive/negative
	balance_color = "red" if summary_data['running_balance'] < 0 else "blue"
	balance_indicator = "red" if summary_data['running_balance'] < 0 else "blue"

	return [
		{
			"value": summary_data['total_invoice_amount'],
			"label": _("Total Invoice Amount"),
			"datatype": "Currency",
			"currency": currency,
			"indicator": "red"
		},
		{
			"value": summary_data['total_payment_received'],
			"label": _("Total Payment Received"),
			"datatype": "Currency",
			"currency": currency,
			"indicator": "green"
		},
		{
			"value": summary_data['running_balance'],
			"label": _("Running Balance"),
			"datatype": "Currency",
			"currency": currency,
			"indicator": balance_indicator
		}
	]