{"add_total_row": 0, "add_translate_data": 0, "columns": [], "creation": "2025-09-22 17:39:24.229101", "disabled": 0, "docstatus": 0, "doctype": "Report", "filters": [], "idx": 0, "is_standard": "Yes", "letterhead": null, "modified": "2025-09-22 17:39:24.229101", "modified_by": "Administrator", "module": "Buildo Frappe", "name": "Test Hiren", "owner": "Administrator", "prepared_report": 0, "ref_doctype": "Customers", "report_name": "Test Hiren", "report_type": "Script Report", "roles": [{"role": "System Manager"}], "timeout": 0}