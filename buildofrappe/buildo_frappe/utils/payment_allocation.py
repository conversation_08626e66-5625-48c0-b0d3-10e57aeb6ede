# Copyright (c) 2025, <PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.utils import flt


@frappe.whitelist()
def recalculate_invoice_balance(invoice_name):
	"""
	Recalculate invoice balance considering payment allocations
	
	Args:
		invoice_name (str): Name of the Client Invoice
		
	Returns:
		float: Updated balance amount
	"""
	try:
		# Get invoice details
		invoice = frappe.get_doc("Client Invoice", invoice_name)
		
		# Calculate base balance (net_payable - tds_amount)
		base_balance = flt(invoice.net_payable) - flt(invoice.tds_amount)
		
		# Get total allocated amount from Payment Allocation
		total_allocated = frappe.db.sql("""
			SELECT COALESCE(SUM(allocated_amount), 0) as total_allocated
			FROM `tabPayment Allocation`
			WHERE client_invoice = %s
		""", (invoice_name,))[0][0]
		
		# Calculate new balance
		new_balance = max(0, base_balance - flt(total_allocated))
		
		# Update the invoice balance
		frappe.db.set_value("Client Invoice", invoice_name, "balance_amount", new_balance)
		
		# Update invoice status
		update_invoice_status(invoice_name, new_balance, base_balance)
		
		# Emit realtime event for balance update
		frappe.publish_realtime(
			"invoice_balance_updated",
			{
				"invoice_name": invoice_name,
				"new_balance": new_balance,
				"total_allocated": flt(total_allocated)
			},
			user=frappe.session.user
		)
		
		return new_balance
		
	except Exception as e:
		frappe.log_error(f"Error recalculating invoice balance for {invoice_name}: {str(e)}")
		frappe.throw(f"Error recalculating invoice balance: {str(e)}")


def update_invoice_status(invoice_name, balance_amount, net_payable):
	"""
	Update invoice status based on balance amount
	
	Args:
		invoice_name (str): Name of the Client Invoice
		balance_amount (float): Current balance amount
		net_payable (float): Net payable amount
	"""
	if balance_amount <= 0:
		status = "Fully Paid"
	elif balance_amount >= net_payable:
		status = "Not Paid"
	else:
		status = "Partially Paid"
	
	frappe.db.set_value("Client Invoice", invoice_name, "status", status)


@frappe.whitelist()
def get_outstanding_invoices_for_payment(customer, project, payment_amount=None):
	"""
	Get outstanding invoices for a customer and project for payment allocation
	
	Args:
		customer (str): Customer name
		project (str): Project name
		payment_amount (float, optional): Payment amount for allocation preview
		
	Returns:
		list: List of outstanding invoices with allocation preview
	"""
	invoices = frappe.get_all(
		"Client Invoice",
		filters={
			"customer": customer,
			"project": project,
			"balance_amount": [">", 0],
			"docstatus": ["in", [0, 1]]  # Allow both draft and submitted invoices
		},
		fields=[
			"name", "invoice_no", "date", "net_payable", 
			"tds_amount", "balance_amount", "status"
		],
		order_by="date asc"
	)
	
	# If payment amount is provided, calculate allocation preview
	if payment_amount and invoices:
		remaining_amount = flt(payment_amount)
		
		for invoice in invoices:
			invoice_balance = flt(invoice.balance_amount)
			
			if remaining_amount <= 0:
				invoice["suggested_allocation"] = 0
			else:
				allocation_amount = min(remaining_amount, invoice_balance)
				invoice["suggested_allocation"] = allocation_amount
				remaining_amount -= allocation_amount
	
	return invoices


@frappe.whitelist()
def allocate_payment_manually(payment_received, allocations):
	"""
	Manually allocate payment to specific invoices
	
	Args:
		payment_received (str): Payment Received document name
		allocations (list): List of allocation dictionaries with invoice and amount
		
	Returns:
		dict: Allocation result summary
	"""
	try:
		# Get payment document
		payment_doc = frappe.get_doc("Payment Received", payment_received)
		
		# Clear existing allocations
		existing_allocations = frappe.get_all(
			"Payment Allocation",
			filters={"payment_received": payment_received},
			fields=["name", "client_invoice", "allocated_amount"]
		)
		
		# Restore invoice balances from existing allocations
		for allocation in existing_allocations:
			current_balance = frappe.db.get_value("Client Invoice", allocation.client_invoice, "balance_amount")
			new_balance = flt(current_balance) + flt(allocation.allocated_amount)
			frappe.db.set_value("Client Invoice", allocation.client_invoice, "balance_amount", new_balance)
			frappe.delete_doc("Payment Allocation", allocation.name, ignore_permissions=True)
		
		# Process new allocations
		total_allocated = 0
		allocation_details = []
		
		for alloc in allocations:
			invoice_name = alloc.get("invoice")
			allocation_amount = flt(alloc.get("amount", 0))
			
			if allocation_amount <= 0:
				continue
			
			# Get current invoice balance
			invoice_balance = frappe.db.get_value("Client Invoice", invoice_name, "balance_amount")
			
			if allocation_amount > invoice_balance:
				frappe.throw(f"Allocation amount {allocation_amount} exceeds invoice balance {invoice_balance} for {invoice_name}")
			
			# Create allocation record
			allocation = frappe.get_doc({
				"doctype": "Payment Allocation",
				"payment_received": payment_received,
				"client_invoice": invoice_name,
				"allocated_amount": allocation_amount,
				"customer": payment_doc.customer,
				"project": payment_doc.project,
				"allocation_date": payment_doc.date,
				"allocation_method": "Manual"
			})
			allocation.insert(ignore_permissions=True)
			
			# Update invoice balance
			new_balance = invoice_balance - allocation_amount
			frappe.db.set_value("Client Invoice", invoice_name, "balance_amount", new_balance)
			
			# Update invoice status
			invoice_data = frappe.db.get_value("Client Invoice", invoice_name, 
											 ["net_payable", "tds_amount"], as_dict=True)
			net_payable = flt(invoice_data.net_payable) - flt(invoice_data.tds_amount)
			update_invoice_status(invoice_name, new_balance, net_payable)
			
			total_allocated += allocation_amount
			allocation_details.append({
				"invoice": invoice_name,
				"allocated_amount": allocation_amount,
				"new_balance": new_balance
			})
		
		# Update payment unallocated amount
		unallocated_amount = flt(payment_doc.amount) - total_allocated
		frappe.db.set_value("Payment Received", payment_received, "unallocated_amount", unallocated_amount)
		
		frappe.db.commit()
		
		return {
			"success": True,
			"total_allocated": total_allocated,
			"unallocated_amount": unallocated_amount,
			"allocation_details": allocation_details
		}
		
	except Exception as e:
		frappe.db.rollback()
		frappe.log_error(f"Error in manual payment allocation: {str(e)}")
		frappe.throw(f"Error in manual payment allocation: {str(e)}")


def on_payment_allocation_change(doc, method):
	"""
	Hook function called when Payment Allocation is created or updated

	Args:
		doc: Payment Allocation document
		method: The method that triggered this hook
	"""
	if doc.client_invoice:
		# Recalculate invoice balance
		recalculate_invoice_balance(doc.client_invoice)


def on_payment_allocation_delete(doc, method):
	"""
	Hook function called when Payment Allocation is deleted

	Args:
		doc: Payment Allocation document
		method: The method that triggered this hook
	"""
	if doc.client_invoice:
		# Restore invoice balance
		current_balance = frappe.db.get_value("Client Invoice", doc.client_invoice, "balance_amount")
		new_balance = flt(current_balance) + flt(doc.allocated_amount)
		frappe.db.set_value("Client Invoice", doc.client_invoice, "balance_amount", new_balance)

		# Update invoice status
		invoice_data = frappe.db.get_value("Client Invoice", doc.client_invoice,
										 ["net_payable", "tds_amount"], as_dict=True)
		if invoice_data:
			net_payable = flt(invoice_data.net_payable) - flt(invoice_data.tds_amount)
			update_invoice_status(doc.client_invoice, new_balance, net_payable)

		# Emit realtime event
		frappe.publish_realtime(
			"invoice_balance_updated",
			{
				"invoice_name": doc.client_invoice,
				"new_balance": new_balance,
				"allocation_deleted": True
			},
			user=frappe.session.user
		)
